"""
AI Dating Agent MVP - 重构版本
简洁、可扩展、易测试的架构

主要组件:
- Core: 核心业务逻辑 (UserManager, AnalysisEngine, MatchEngine)
- Services: 外部服务集成 (WhatsApp, Voice, LinkedIn)
- Config: 配置管理
- Utils: 共享工具和数据模型
"""

from .agent_interface import AgentInterface
from .agent_factory import (
    AgentFactory, agent_factory, initialize_agent_system,
    get_agent_interface, get_system_status, cleanup_agent_system
)
from .core import DatabaseManager, RegistrationStateManager, ProfileManager, MemoryManager
from .services import SMSService, VoiceService, LinkedInService
from .config import CoreConfig, ServiceConfig, Templates
from .utils import User, UserStatus, VerificationStatus, UserPermission, APIResponse

__version__ = "2.0.0-mvp"

__all__ = [
    # 主接口
    'AgentInterface',

    # 工厂和初始化
    'AgentFactory', 'agent_factory', 'initialize_agent_system',
    'get_agent_interface', 'get_system_status', 'cleanup_agent_system',

    # 核心组件
    'DatabaseManager',
    'RegistrationStateManager',
    'ProfileManager',
    'MemoryManager',

    # 服务组件
    'SMSService',
    'VoiceService',
    'LinkedInService',

    # 配置
    'CoreConfig',
    'ServiceConfig',
    'Templates',

    # 数据模型
    'User',
    'UserStatus',
    'VerificationStatus',
    'UserPermission',
    'APIResponse'
]

def create_agent(db_connection=None) -> AgentInterface:
    """创建Agent实例的便捷函数 - 兼容性保留"""
    return AgentInterface(db_connection)

# 新的推荐方式：使用工厂模式
async def create_agent_system(redis_client=None) -> AgentInterface:
    """创建完整Agent系统的推荐方式"""
    await initialize_agent_system(redis_client)
    return get_agent_interface()