"""
AgentInterface - Web API统一接口
MVP版本的Web应用接口层
"""

from typing import Dict, Any, Optional
from datetime import datetime
import logging

from .utils.data_models import (
    User, UserRegistrationRequest, SMSVerificationRequest, LoginRequest,
    APIResponse, UserStatus, ConversationStage
)

logger = logging.getLogger(__name__)

class AgentInterface:
    """AI Agent Web API接口 - MVP版本"""

    def __init__(self, db_manager=None, registration_manager=None, profile_manager=None,
                 memory_manager=None, sms_service=None):
        """初始化Agent接口 - 简化版"""
        self.db_manager = db_manager
        self.registration_manager = registration_manager
        self.profile_manager = profile_manager
        self.memory_manager = memory_manager
        self.sms_service = sms_service

        logger.info("AgentInterface initialized for Web API")

    # ============ 用户认证相关接口 ============

    async def register_user(self, registration_data: UserRegistrationRequest) -> APIResponse:
        """用户注册 - 暂时简化实现"""
        try:
            # TODO: 使用新的registration_manager实现
            return APIResponse.success_response(
                {"message": "Registration functionality will be implemented with new architecture"},
                "REGISTRATION_PENDING"
            )

            # 保存用户
            saved_user = await self.user_manager.create_user(user)

            # 发送SMS验证码
            sms_result = await self.sms_service.send_verification_code(registration_data.phone_number)

            if not sms_result.success:
                return APIResponse.error_response("Failed to send verification code", "SMS_SEND_FAILED")

            return APIResponse.success_response({
                "user_id": saved_user.user_id,
                "message": "Registration successful. Please verify your phone number.",
                "verification_required": True
            })

        except Exception as e:
            logger.error(f"User registration failed: {e}")
            return APIResponse.error_response(str(e), "REGISTRATION_FAILED")

    async def send_sms_verification(self, phone_number: str) -> APIResponse:
        """发送SMS验证码 - 独立端点"""
        try:
            # 检查用户是否存在
            existing_user = await self.user_manager.get_user_by_phone(phone_number)
            if not existing_user:
                return APIResponse.error_response("User not found. Please register first.", "USER_NOT_FOUND")

            # 发送SMS验证码
            sms_result = await self.sms_service.send_verification_code(phone_number)

            if not sms_result.success:
                return APIResponse.error_response("Failed to send verification code", "SMS_SEND_FAILED")

            return APIResponse.success_response({
                "message": "Verification code sent successfully",
                "phone_number": phone_number
            })

        except Exception as e:
            logger.error(f"SMS sending failed: {e}")
            return APIResponse.error_response(str(e), "SMS_SEND_FAILED")

    async def verify_sms(self, verification_data: SMSVerificationRequest) -> APIResponse:
        """验证SMS验证码"""
        try:
            # 验证SMS代码
            verification_result = await self.sms_service.verify_code(
                verification_data.phone_number,
                verification_data.verification_code
            )

            if not verification_result.success:
                return APIResponse.error_response("Invalid verification code", "INVALID_CODE")

            # 更新用户状态
            user = await self.user_manager.get_user_by_phone(verification_data.phone_number)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            user.verify_sms()
            await self.user_manager.update_user(user)

            # 生成JWT Token
            access_token = self.user_manager.create_access_token(user.user_id)
            refresh_token = self.user_manager.create_refresh_token(user.user_id)

            return APIResponse.success_response({
                "user_id": user.user_id,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "user_status": user.status.value,
                "verification_status": user.verification_status.value,
                "next_step": "voice_interview",
                "message": "Phone number verified successfully!"
            })

        except Exception as e:
            logger.error(f"SMS verification failed: {e}")
            return APIResponse.error_response(str(e), "VERIFICATION_FAILED")

    async def login_user(self, login_data: LoginRequest) -> APIResponse:
        """用户登录"""
        try:
            user = await self.user_manager.get_user_by_phone(login_data.phone_number)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            if not user.sms_verified:
                return APIResponse.error_response("Phone number not verified", "PHONE_NOT_VERIFIED")

            # 如果提供了验证码，验证SMS
            if login_data.verification_code:
                verification_result = await self.sms_service.verify_code(
                    login_data.phone_number,
                    login_data.verification_code
                )
                if not verification_result.success:
                    return APIResponse.error_response("Invalid verification code", "INVALID_CODE")

            # 更新最后登录时间
            user.last_login = datetime.now()
            await self.user_manager.update_user(user)

            # 生成JWT Token
            access_token = self.user_manager.create_access_token(user.user_id)
            refresh_token = self.user_manager.create_refresh_token(user.user_id)

            return APIResponse.success_response({
                "user_id": user.user_id,
                "access_token": access_token,
                "refresh_token": refresh_token,
                "user_status": user.status.value,
                "verification_status": user.verification_status.value,
                "current_stage": user.current_conversation_stage.value,
                "message": "Login successful!"
            })

        except Exception as e:
            logger.error(f"User login failed: {e}")
            return APIResponse.error_response(str(e), "LOGIN_FAILED")

    async def logout_user(self, user_id: str) -> APIResponse:
        """用户登出"""
        try:
            user = await self.user_manager.get_user_by_phone(user_id) if user_id.startswith('+') else await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 尝试使token失效（如果user_manager支持token黑名单）
            try:
                # 调用user_manager的logout方法来处理token失效
                logout_result = self.user_manager.logout_user(user_id)
                if not logout_result:
                    logger.warning(f"User manager logout failed for user {user_id}")
            except AttributeError:
                # user_manager可能还没有实现logout_user方法，这是正常的
                logger.info("User manager logout method not implemented, token will expire naturally")
            except Exception as logout_error:
                logger.warning(f"Logout operation failed: {logout_error}")

            # 更新最后活动时间（可选）
            try:
                user.last_activity = datetime.now()
                await self.user_manager.update_user(user)
            except Exception as update_error:
                logger.warning(f"Failed to update user last activity: {update_error}")

            return APIResponse.success_response({
                "user_id": user.user_id,
                "logged_out": True,
                "message": "Logout successful!"
            })

        except Exception as e:
            logger.error(f"User logout failed: {e}")
            return APIResponse.error_response(str(e), "LOGOUT_FAILED")

    def refresh_token(self, refresh_token: str) -> APIResponse:
        """刷新访问令牌"""
        try:
            # 验证刷新令牌并获取用户ID
            user_id = self.user_manager.verify_refresh_token(refresh_token)
            if not user_id:
                return APIResponse.error_response("Invalid refresh token", "INVALID_REFRESH_TOKEN")

            # 生成新的访问令牌
            new_access_token = self.user_manager.create_access_token(user_id)

            return APIResponse.success_response({
                "access_token": new_access_token,
                "message": "Token refreshed successfully"
            })

        except Exception as e:
            logger.error(f"Token refresh failed: {e}")
            return APIResponse.error_response(str(e), "TOKEN_REFRESH_FAILED")

    def verify_access_token(self, token: str) -> APIResponse:
        """验证访问令牌"""
        try:
            user_id = self.user_manager.verify_access_token(token)
            if not user_id:
                return APIResponse.error_response("Invalid or expired token", "INVALID_TOKEN")

            return APIResponse.success_response({
                "user_id": user_id,
                "valid": True
            })

        except Exception as e:
            logger.error(f"Token verification failed: {e}")
            return APIResponse.error_response(str(e), "TOKEN_VERIFICATION_FAILED")

    # ============ 语音通话相关接口 ============

    async def handle_voice_call(self, call_sid: str, from_number: str) -> str:
        """处理语音通话开始"""
        try:
            # 根据电话号码查找用户
            user = await self.user_manager.get_user_by_phone(from_number)
            if not user:
                return self.voice_service.generate_error_response("User not found")

            # 检查用户状态 - 使用与SMS服务相同的验证逻辑
            if not user.sms_verified:
                return self.voice_service.generate_error_response("Please complete phone verification first")

            # 开始语音面试
            return await self.voice_service.handle_incoming_call(call_sid, from_number)

        except Exception as e:
            logger.error(f"Voice call handling failed: {e}")
            return self.voice_service.generate_error_response("System error")

    async def process_speech_input(self, call_sid: str, speech_text: str) -> str:
        """处理语音输入"""
        try:
            return await self.voice_service.process_speech_input(call_sid, speech_text)
        except Exception as e:
            logger.error(f"Speech processing failed: {e}")
            return self.voice_service.generate_error_response("Speech processing error")

    async def get_voice_call_status(self, user_id: str) -> APIResponse:
        """获取语音通话状态"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            voice_status = await self.voice_service.get_call_status(user_id)

            return APIResponse.success_response({
                "user_id": user_id,
                "voice_completed": user.voice_call_completed,
                "current_stage": user.current_conversation_stage.value,
                "call_status": voice_status
            })

        except Exception as e:
            logger.error(f"Voice status check failed: {e}")
            return APIResponse.error_response(str(e), "STATUS_CHECK_FAILED")

    async def initiate_voice_call(self, user_id: str) -> APIResponse:
        """发起语音通话"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            if not user.sms_verified:
                return APIResponse.error_response(
                    "SMS verification must be completed before voice interview",
                    "SMS_NOT_VERIFIED"
                )

            if user.voice_call_completed:
                return APIResponse.error_response(
                    "Voice interview already completed",
                    "VOICE_ALREADY_COMPLETED"
                )

            return await self.voice_service.initiate_call(user.phone_number, user_id)

        except Exception as e:
            logger.error(f"Voice call initiation failed: {e}")
            return APIResponse.error_response(str(e), "VOICE_CALL_INITIATION_FAILED")

    async def get_voice_analysis(self, user_id: str) -> APIResponse:
        """获取语音分析结果"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            if not user.voice_call_completed:
                return APIResponse.error_response(
                    "Voice interview not completed yet",
                    "VOICE_NOT_COMPLETED"
                )

            return await self.voice_service.get_analysis_results(user_id)

        except Exception as e:
            logger.error(f"Voice analysis retrieval failed: {e}")
            return APIResponse.error_response(str(e), "VOICE_ANALYSIS_FAILED")

    async def retry_voice_interview(self, user_id: str) -> APIResponse:
        """重新开始语音面试"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            if not user.sms_verified:
                return APIResponse.error_response(
                    "SMS verification must be completed before voice interview",
                    "SMS_NOT_VERIFIED"
                )

            return await self.voice_service.reset_voice_status(user_id)

        except Exception as e:
            logger.error(f"Voice interview retry failed: {e}")
            return APIResponse.error_response(str(e), "VOICE_RETRY_FAILED")

    async def handle_voice_webhook(self, webhook_type: str, webhook_data: dict) -> APIResponse:
        """处理语音webhook"""
        try:
            if webhook_type == "incoming":
                call_sid = webhook_data.get("CallSid")
                from_number = webhook_data.get("From")
                if not call_sid or not from_number:
                    return APIResponse.error_response("Missing required call data", "INVALID_WEBHOOK_DATA")

                twiml_response = await self.voice_service.handle_incoming_call(call_sid, from_number)
                return APIResponse.success_response({"twiml": twiml_response})

            elif webhook_type == "recording":
                call_sid = webhook_data.get("CallSid")
                recording_url = webhook_data.get("RecordingUrl")
                recording_duration = webhook_data.get("RecordingDuration")
                if not call_sid or not recording_url:
                    return APIResponse.error_response("Missing required recording data", "INVALID_WEBHOOK_DATA")

                return await self.voice_service.process_recording(call_sid, recording_url, recording_duration)

            elif webhook_type == "status":
                call_sid = webhook_data.get("CallSid")
                call_status = webhook_data.get("CallStatus")
                call_duration = webhook_data.get("CallDuration")
                if not call_sid or not call_status:
                    return APIResponse.error_response("Missing required status data", "INVALID_WEBHOOK_DATA")

                return await self.voice_service.handle_call_status_update(call_sid, call_status, call_duration)

            elif webhook_type == "outgoing":
                call_sid = webhook_data.get("CallSid")
                to_number = webhook_data.get("To")  # 用户号码
                from_number = webhook_data.get("From")  # 系统号码
                if not call_sid or not to_number:
                    return APIResponse.error_response("Missing required call data", "INVALID_WEBHOOK_DATA")

                twiml_response = await self.voice_service.handle_outgoing_call(call_sid, to_number, from_number)
                return APIResponse.success_response({"twiml": twiml_response})

            elif webhook_type == "speech":
                call_sid = webhook_data.get("CallSid")
                speech_result = webhook_data.get("SpeechResult", "")
                confidence = webhook_data.get("Confidence", 1.0)
                if not call_sid:
                    return APIResponse.error_response("Missing CallSid", "INVALID_WEBHOOK_DATA")

                twiml_response = await self.voice_service.process_speech_input(call_sid, speech_result, confidence)
                return APIResponse.success_response({"twiml": twiml_response})

            else:
                return APIResponse.error_response("Unknown webhook type", "UNKNOWN_WEBHOOK_TYPE")

        except Exception as e:
            logger.error(f"Voice webhook handling failed: {e}")
            return APIResponse.error_response(str(e), "WEBHOOK_HANDLING_FAILED")

    async def handle_speech_timeout(self, call_sid: str) -> str:
        """处理语音输入超时"""
        try:
            return await self.voice_service.handle_speech_timeout(call_sid)
        except Exception as e:
            logger.error(f"Speech timeout handling failed: {e}")
            return self.voice_service.generate_error_response("Timeout handling error")

    # ============ LinkedIn验证相关接口 ============

    async def submit_linkedin_profile(self, user_id: str, linkedin_url: str) -> APIResponse:
        """提交LinkedIn资料验证"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            if not user.voice_call_completed:
                return APIResponse.error_response("Please complete voice interview first", "VOICE_NOT_COMPLETED")

            # 开始LinkedIn验证
            verification_result = self.linkedin_service.verify_profile(user_id, linkedin_url)

            if verification_result.success:
                user.verify_linkedin()
                self.user_manager.update_user(user)

            return verification_result

        except Exception as e:
            logger.error(f"LinkedIn verification failed: {e}")
            return APIResponse.error_response(str(e), "LINKEDIN_VERIFICATION_FAILED")

    async def get_linkedin_verification_status(self, user_id: str) -> APIResponse:
        """获取LinkedIn验证状态"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            linkedin_status = self.linkedin_service.get_verification_status(user_id)

            return APIResponse.success_response({
                "user_id": user_id,
                "linkedin_verified": user.linkedin_verified,
                "verification_level": user.verification_level.value,
                "linkedin_status": linkedin_status
            })

        except Exception as e:
            logger.error(f"LinkedIn status check failed: {e}")
            return APIResponse.error_response(str(e), "LINKEDIN_STATUS_CHECK_FAILED")

    # ============ 用户画像相关接口 ============

    async def get_user_profile(self, user_id: str) -> APIResponse:
        """获取用户完整画像"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 获取用户基础信息
            profile_data = {
                "user_id": user_id,
                "basic_info": {
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "age": user.age,
                    "city": user.city,
                    "profession": user.profession
                },
                "verification_status": {
                    "sms_verified": user.sms_verified,
                    "voice_completed": user.voice_call_completed,
                    "linkedin_verified": user.linkedin_verified,
                    "verification_level": user.verification_level.value
                },
                "current_stage": user.current_conversation_stage.value
            }

            # 获取画像卡片
            if user.voice_call_completed:
                profile_cards = self.analysis_engine.get_profile_cards(user_id)
                profile_data["profile_cards"] = profile_cards

            return APIResponse.success_response(profile_data)

        except Exception as e:
            logger.error(f"Profile retrieval failed: {e}")
            return APIResponse.error_response(str(e), "PROFILE_RETRIEVAL_FAILED")

    async def get_profile_cards(self, user_id: str) -> APIResponse:
        """获取用户画像卡片"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            if not user.voice_call_completed:
                return APIResponse.error_response("Voice interview not completed", "VOICE_NOT_COMPLETED")

            profile_cards = await self.analysis_engine.get_profile_cards(user_id)

            return APIResponse.success_response({
                "user_id": user_id,
                "profile_cards": profile_cards,
                "total_cards": len(profile_cards) if profile_cards else 0
            })

        except Exception as e:
            logger.error(f"Profile cards retrieval failed: {e}")
            return APIResponse.error_response(str(e), "PROFILE_CARDS_FAILED")

    async def generate_final_profile(self, user_id: str) -> APIResponse:
        """生成最终用户画像"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            if not user.voice_call_completed:
                return APIResponse.error_response("Voice interview not completed", "VOICE_NOT_COMPLETED")

            if not user.linkedin_verified:
                return APIResponse.error_response("LinkedIn verification not completed", "LINKEDIN_NOT_VERIFIED")

            # 生成最终画像
            final_profile = await self.analysis_engine.generate_final_profile(user_id)

            return APIResponse.success_response({
                "user_id": user_id,
                "final_profile": final_profile,
                "generated_at": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Final profile generation failed: {e}")
            return APIResponse.error_response(str(e), "FINAL_PROFILE_GENERATION_FAILED")

    async def get_profile_analysis(self, user_id: str) -> APIResponse:
        """获取用户画像分析详情"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            if not user.voice_call_completed:
                return APIResponse.error_response("Voice interview not completed", "VOICE_NOT_COMPLETED")

            # 获取详细分析
            analysis_result = await self.analysis_engine.get_detailed_analysis(user_id)

            return APIResponse.success_response({
                "user_id": user_id,
                "analysis": analysis_result
            })

        except Exception as e:
            logger.error(f"Profile analysis retrieval failed: {e}")
            return APIResponse.error_response(str(e), "PROFILE_ANALYSIS_FAILED")

    async def get_profile_status(self, user_id: str) -> APIResponse:
        """获取用户画像生成状态"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            status_data = {
                "user_id": user_id,
                "voice_completed": user.voice_call_completed,
                "linkedin_verified": user.linkedin_verified,
                "profile_cards_available": user.voice_call_completed,
                "final_profile_available": user.voice_call_completed and user.linkedin_verified,
                "verification_status": user.verification_status.value,
                "current_stage": user.current_conversation_stage.value
            }

            return APIResponse.success_response(status_data)

        except Exception as e:
            logger.error(f"Profile status check failed: {e}")
            return APIResponse.error_response(str(e), "PROFILE_STATUS_FAILED")

    # ============ 匹配推荐相关接口 ============

    async def get_match_recommendations(self, user_id: str, limit: int = 10) -> APIResponse:
        """获取用户匹配推荐"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            if not user.voice_call_completed:
                return APIResponse.error_response("Please complete voice interview first", "VOICE_NOT_COMPLETED")

            if not user.linkedin_verified:
                return APIResponse.error_response("Please complete LinkedIn verification first", "LINKEDIN_NOT_VERIFIED")

            # 获取匹配推荐
            matches = await self.match_engine.find_matches(user_id, limit=limit)

            # 格式化匹配数据
            formatted_matches = []
            for match in matches:
                match_user = await self.user_manager.get_user(match.user2_id)
                if match_user:
                    formatted_match = {
                        "match_id": match.match_id,
                        "user_info": {
                            "user_id": match_user.user_id,
                            "first_name": match_user.first_name,
                            "age": match_user.age,
                            "city": match_user.city,
                            "profession": match_user.profession
                        },
                        "compatibility_score": match.match_score,
                        "compatibility_reasons": match.recommendation_reason,
                        "created_at": match.created_at.isoformat()
                    }
                    formatted_matches.append(formatted_match)

            return APIResponse.success_response({
                "user_id": user_id,
                "matches": formatted_matches,
                "total_matches": len(formatted_matches)
            })

        except Exception as e:
            logger.error(f"Match retrieval failed: {e}")
            return APIResponse.error_response(str(e), "MATCH_RETRIEVAL_FAILED")

    async def process_match_feedback(self, user_id: str, match_id: str, action: str) -> APIResponse:
        """处理匹配反馈（喜欢/跳过）"""
        try:
            if action not in ["interested", "passed"]:
                return APIResponse.error_response("Invalid action", "INVALID_ACTION")

            # 记录用户反馈
            feedback_result = await self.match_engine.record_match_feedback(user_id, match_id, action)

            if not feedback_result.success:
                return feedback_result

            # 检查是否匹配成功
            if action == "interested":
                match_result = await self.match_engine.check_mutual_interest(match_id)
                if match_result.success and match_result.data.get("mutual_match"):
                    return APIResponse.success_response({
                        "action_recorded": True,
                        "mutual_match": True,
                        "message": "It's a match! You can now start chatting."
                    })

            return APIResponse.success_response({
                "action_recorded": True,
                "mutual_match": False,
                "message": "Feedback recorded successfully."
            })

        except Exception as e:
            logger.error(f"Match feedback processing failed: {e}")
            return APIResponse.error_response(str(e), "FEEDBACK_PROCESSING_FAILED")

    async def get_match_history(self, user_id: str, page: int = 1, limit: int = 20) -> APIResponse:
        """获取匹配历史"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 获取匹配历史
            history_result = await self.match_engine.get_match_history(user_id, page=page, limit=limit)

            return APIResponse.success_response({
                "user_id": user_id,
                "history": history_result,
                "page": page,
                "limit": limit
            })

        except Exception as e:
            logger.error(f"Match history retrieval failed: {e}")
            return APIResponse.error_response(str(e), "MATCH_HISTORY_FAILED")

    async def get_mutual_matches(self, user_id: str) -> APIResponse:
        """获取相互匹配的用户"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 获取相互匹配
            mutual_matches = await self.match_engine.get_mutual_matches(user_id)

            return APIResponse.success_response({
                "user_id": user_id,
                "mutual_matches": mutual_matches,
                "total_matches": len(mutual_matches) if mutual_matches else 0
            })

        except Exception as e:
            logger.error(f"Mutual matches retrieval failed: {e}")
            return APIResponse.error_response(str(e), "MUTUAL_MATCHES_FAILED")

    async def get_match_stats(self, user_id: str) -> APIResponse:
        """获取匹配统计信息"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 获取匹配统计
            stats = await self.match_engine.get_user_match_statistics(user_id)

            return APIResponse.success_response({
                "user_id": user_id,
                "statistics": stats
            })

        except Exception as e:
            logger.error(f"Match statistics retrieval failed: {e}")
            return APIResponse.error_response(str(e), "MATCH_STATS_FAILED")

    # ============ 用户偏好设置接口 ============

    async def get_user_preferences(self, user_id: str) -> APIResponse:
        """获取用户偏好设置"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            preferences = await self.user_manager.get_user_preferences(user_id)

            return APIResponse.success_response({
                "user_id": user_id,
                "preferences": preferences
            })

        except Exception as e:
            logger.error(f"Preferences retrieval failed: {e}")
            return APIResponse.error_response(str(e), "PREFERENCES_RETRIEVAL_FAILED")

    async def update_user_preferences(self, user_id: str, preferences_data: Dict[str, Any]) -> APIResponse:
        """更新用户偏好设置"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 更新偏好设置
            updated_preferences = await self.user_manager.update_user_preferences(user_id, preferences_data)

            return APIResponse.success_response({
                "user_id": user_id,
                "preferences": updated_preferences,
                "message": "Preferences updated successfully"
            })

        except Exception as e:
            logger.error(f"Preferences update failed: {e}")
            return APIResponse.error_response(str(e), "PREFERENCES_UPDATE_FAILED")

    async def update_user_profile(self, user_id: str, profile_data: Dict[str, Any]) -> APIResponse:
        """更新用户基础信息"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 更新用户信息
            for key, value in profile_data.items():
                if hasattr(user, key) and value is not None:
                    setattr(user, key, value)

            # 保存更新
            await self.user_manager.update_user(user)

            return APIResponse.success_response({
                "user_id": user_id,
                "updated_fields": list(profile_data.keys()),
                "message": "Profile updated successfully"
            })

        except Exception as e:
            logger.error(f"Profile update failed: {e}")
            return APIResponse.error_response(str(e), "PROFILE_UPDATE_FAILED")

    async def get_user_status_details(self, user_id: str) -> APIResponse:
        """获取用户状态详情"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 获取用户权限
            available_permissions = await self.user_manager.get_available_permissions(user_id)

            status_details = {
                "user_id": user_id,
                "status": user.status.value,
                "verification_status": user.verification_status.value,
                "verification_level": user.verification_level.value,
                "sms_verified": user.sms_verified,
                "voice_call_completed": user.voice_call_completed,
                "linkedin_verified": user.linkedin_verified,
                "current_stage": user.current_conversation_stage.value,
                "available_permissions": [p.value for p in available_permissions],
                "created_at": user.created_at.isoformat(),
                "last_login": user.last_login.isoformat() if user.last_login else None
            }

            return APIResponse.success_response(status_details)

        except Exception as e:
            logger.error(f"User status details retrieval failed: {e}")
            return APIResponse.error_response(str(e), "STATUS_DETAILS_FAILED")

    async def delete_user_account(self, user_id: str) -> APIResponse:
        """删除用户账户（软删除）"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            # 软删除用户账户
            user.status = UserStatus.INACTIVE
            user.updated_at = datetime.now()

            await self.user_manager.update_user(user)

            return APIResponse.success_response({
                "user_id": user_id,
                "message": "Account deactivated successfully",
                "deactivated_at": datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Account deletion failed: {e}")
            return APIResponse.error_response(str(e), "ACCOUNT_DELETION_FAILED")

        except Exception as e:
            logger.error(f"Preferences update failed: {e}")
            return APIResponse.error_response(str(e), "PREFERENCES_UPDATE_FAILED")

    # ============ 系统状态和管理接口 ============

    # ============ ConversationRelay支持方法 ============

    async def get_user_by_call_sid(self, call_sid: str):
        """获取call_sid对应的用户ID - ConversationRelay支持"""
        if not self.voice_service:
            logger.error("Voice service not available")
            return None

        if not hasattr(self.voice_service, 'conversation_agent'):
            logger.error("Conversation agent not available in voice service")
            return None

        return await self.voice_service.conversation_agent.get_user_by_call_sid(call_sid)

    async def process_voice_conversation(self, user_id: str, user_text: str, call_sid: str) -> dict:
        """处理语音对话 - ConversationRelay支持"""
        if not self.voice_service:
            logger.error("Voice service not available")
            return {
                "success": False,
                "response": "Voice service unavailable",
                "should_end": True,
                "confidence": 0.0
            }

        return await self.voice_service.process_voice_conversation(user_id, user_text, call_sid)

    # ============ 系统健康检查 ============

    def get_health_status(self) -> APIResponse:
        """获取系统健康状态"""
        try:
            health_data = {
                "timestamp": datetime.now().isoformat(),
                "services": {
                    "user_manager": self.user_manager is not None,
                    "sms_service": self.sms_service is not None,
                    "voice_service": self.voice_service is not None,
                    "linkedin_service": self.linkedin_service is not None,
                    "analysis_engine": self.analysis_engine is not None,
                    "match_engine": self.match_engine is not None
                },
                "status": "healthy"
            }

            # 检查各服务状态
            if self.user_manager:
                health_data["database"] = self.user_manager.check_database_connection()

            if self.sms_service:
                health_data["sms_service"] = self.sms_service.check_service_status()

            return APIResponse.success_response(health_data)

        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return APIResponse.error_response(str(e), "HEALTH_CHECK_FAILED")

    async def get_user_statistics(self, user_id: str) -> APIResponse:
        """获取用户统计信息"""
        try:
            user = await self.user_manager.get_user(user_id)
            if not user:
                return APIResponse.error_response("User not found", "USER_NOT_FOUND")

            stats = {
                "user_id": user_id,
                "registration_date": user.created_at.isoformat(),
                "last_login": user.last_login.isoformat(),
                "verification_status": {
                    "sms_verified": user.sms_verified,
                    "voice_completed": user.voice_call_completed,
                    "linkedin_verified": user.linkedin_verified
                },
                "current_stage": user.current_conversation_stage.value,
                "profile_completion": self._calculate_profile_completion(user)
            }

            # 添加匹配统计
            if user.status == UserStatus.REGISTERED:
                match_stats = self.match_engine.get_user_match_statistics(user_id)
                stats["match_statistics"] = match_stats

            return APIResponse.success_response(stats)

        except Exception as e:
            logger.error(f"User statistics retrieval failed: {e}")
            return APIResponse.error_response(str(e), "STATISTICS_RETRIEVAL_FAILED")

    def _calculate_profile_completion(self, user: User) -> Dict[str, Any]:
        """计算用户档案完成度"""
        completion_score = 0
        total_steps = 5

        # 基础信息完成度
        if user.first_name and user.last_name:
            completion_score += 1

        # SMS验证完成度
        if user.sms_verified:
            completion_score += 1

        # 语音通话完成度
        if user.voice_call_completed:
            completion_score += 1

        # LinkedIn验证完成度
        if user.linkedin_verified:
            completion_score += 1

        # 偏好设置完成度
        preferences = self.user_manager.get_user_preferences(user.user_id)
        if preferences:
            completion_score += 1

        completion_percentage = (completion_score / total_steps) * 100

        return {
            "completion_percentage": completion_percentage,
            "completed_steps": completion_score,
            "total_steps": total_steps,
            "next_step": self._get_next_step(user)
        }

    def _get_next_step(self, user: User) -> str:
        """获取用户下一步操作"""
        if not user.sms_verified:
            return "verify_phone"
        elif not user.voice_call_completed:
            return "complete_voice_interview"
        elif not user.linkedin_verified:
            return "verify_linkedin"
        elif user.current_conversation_stage != ConversationStage.MATCHING_ACTIVE:
            return "activate_matching"
        else:
            return "browse_matches"
