"""
DatabaseManager - 简化的数据库管理器
专注于注册流程和用户档案管理
"""

import json
import logging
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseManager:
    """简化的异步数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.is_connected = False
        
        # 内存存储备用 (开发/测试用)
        self._memory_store = {
            'users': {},
            'user_profiles': {},
            'conversations': {},
            'conversation_messages': {},
            'user_context_cache': {},
            'voice_sessions': {}
        }
        
        logger.info("DatabaseManager initialized")
    
    async def connect(self) -> bool:
        """连接数据库"""
        try:
            # 检查异步数据库连接
            from backend.database.connection import test_async_database_connection
            
            result = await test_async_database_connection()
            if result["status"] == "connected":
                self.is_connected = True
                logger.info("Database connection established")
                return True
            else:
                logger.warning(f"Database connection failed: {result.get('error', 'Unknown error')}")
                logger.info("Using memory store as fallback")
                self.is_connected = False
                return False
                
        except Exception as e:
            logger.error(f"Database connection error: {e}")
            logger.info("Using memory store as fallback")
            self.is_connected = False
            return False
    
    # ============ 用户管理 ============
    
    async def create_user(self, user_id: str, phone_number: str, name: str = None) -> bool:
        """创建新用户"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        INSERT INTO users (user_id, phone_number, name, status, created_at, updated_at)
                        VALUES (:user_id, :phone_number, :name, 'pending', NOW(), NOW())
                        ON CONFLICT (user_id) DO UPDATE SET
                            phone_number = EXCLUDED.phone_number,
                            name = EXCLUDED.name,
                            updated_at = NOW()
                    """)
                    await session.execute(sql, {
                        "user_id": user_id,
                        "phone_number": phone_number,
                        "name": name
                    })
                    await session.commit()
            else:
                # 内存存储
                self._memory_store['users'][user_id] = {
                    'user_id': user_id,
                    'phone_number': phone_number,
                    'name': name,
                    'status': 'pending',
                    'created_at': datetime.now(),
                    'updated_at': datetime.now()
                }
            
            logger.info(f"Created user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create user {user_id}: {e}")
            return False
    
    async def update_user_status(self, user_id: str, status: str) -> bool:
        """更新用户状态"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        UPDATE users 
                        SET status = :status, updated_at = NOW()
                        WHERE user_id = :user_id
                    """)
                    result = await session.execute(sql, {
                        "user_id": user_id,
                        "status": status
                    })
                    await session.commit()
                    return result.rowcount > 0
            else:
                # 内存存储
                if user_id in self._memory_store['users']:
                    self._memory_store['users'][user_id]['status'] = status
                    self._memory_store['users'][user_id]['updated_at'] = datetime.now()
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Failed to update user status {user_id}: {e}")
            return False
    
    async def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("SELECT * FROM users WHERE user_id = :user_id")
                    result = await session.execute(sql, {"user_id": user_id})
                    row = result.fetchone()
                    if row:
                        return dict(row._mapping)
                    return None
            else:
                # 内存存储
                return self._memory_store['users'].get(user_id)
                
        except Exception as e:
            logger.error(f"Failed to get user {user_id}: {e}")
            return None
    
    # ============ 用户档案管理 ============
    
    async def create_user_profile(self, user_id: str, profile_data: Dict[str, Any] = None) -> bool:
        """创建用户档案"""
        try:
            if profile_data is None:
                profile_data = {}
            
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        INSERT INTO user_profiles (user_id, profile_data, confidence_scores, last_updated, conversation_count)
                        VALUES (:user_id, :profile_data, '{}', NOW(), 0)
                        ON CONFLICT (user_id) DO UPDATE SET
                            profile_data = EXCLUDED.profile_data,
                            last_updated = NOW()
                    """)
                    await session.execute(sql, {
                        "user_id": user_id,
                        "profile_data": json.dumps(profile_data)
                    })
                    await session.commit()
            else:
                # 内存存储
                self._memory_store['user_profiles'][user_id] = {
                    'user_id': user_id,
                    'profile_data': profile_data,
                    'confidence_scores': {},
                    'last_updated': datetime.now(),
                    'conversation_count': 0
                }
            
            logger.info(f"Created profile for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create profile for user {user_id}: {e}")
            return False
    
    async def get_user_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取用户档案"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("SELECT * FROM user_profiles WHERE user_id = :user_id")
                    result = await session.execute(sql, {"user_id": user_id})
                    row = result.fetchone()
                    if row:
                        data = dict(row._mapping)
                        # 解析JSON字段
                        if isinstance(data.get('profile_data'), str):
                            data['profile_data'] = json.loads(data['profile_data'])
                        if isinstance(data.get('confidence_scores'), str):
                            data['confidence_scores'] = json.loads(data['confidence_scores'])
                        return data
                    return None
            else:
                # 内存存储
                return self._memory_store['user_profiles'].get(user_id)
                
        except Exception as e:
            logger.error(f"Failed to get profile for user {user_id}: {e}")
            return None
    
    async def update_user_profile(self, user_id: str, profile_data: Dict[str, Any], 
                                confidence_scores: Dict[str, float] = None) -> bool:
        """更新用户档案"""
        try:
            if confidence_scores is None:
                confidence_scores = {}
            
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        UPDATE user_profiles 
                        SET profile_data = :profile_data,
                            confidence_scores = :confidence_scores,
                            last_updated = NOW(),
                            conversation_count = conversation_count + 1
                        WHERE user_id = :user_id
                    """)
                    result = await session.execute(sql, {
                        "user_id": user_id,
                        "profile_data": json.dumps(profile_data),
                        "confidence_scores": json.dumps(confidence_scores)
                    })
                    await session.commit()
                    return result.rowcount > 0
            else:
                # 内存存储
                if user_id in self._memory_store['user_profiles']:
                    profile = self._memory_store['user_profiles'][user_id]
                    profile['profile_data'] = profile_data
                    profile['confidence_scores'] = confidence_scores
                    profile['last_updated'] = datetime.now()
                    profile['conversation_count'] += 1
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Failed to update profile for user {user_id}: {e}")
            return False
    
    # ============ 对话管理 ============
    
    async def create_conversation(self, user_id: str, conversation_type: str) -> Optional[str]:
        """创建新对话"""
        try:
            conversation_id = str(uuid.uuid4())
            
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        INSERT INTO conversations (id, user_id, type, status, started_at)
                        VALUES (:id, :user_id, :type, 'active', NOW())
                    """)
                    await session.execute(sql, {
                        "id": conversation_id,
                        "user_id": user_id,
                        "type": conversation_type
                    })
                    await session.commit()
            else:
                # 内存存储
                self._memory_store['conversations'][conversation_id] = {
                    'id': conversation_id,
                    'user_id': user_id,
                    'type': conversation_type,
                    'status': 'active',
                    'started_at': datetime.now()
                }
            
            logger.info(f"Created conversation {conversation_id} for user {user_id}")
            return conversation_id
            
        except Exception as e:
            logger.error(f"Failed to create conversation for user {user_id}: {e}")
            return None
    
    async def add_message(self, conversation_id: str, role: str, content: str, 
                         metadata: Dict[str, Any] = None) -> bool:
        """添加消息到对话"""
        try:
            message_id = str(uuid.uuid4())
            if metadata is None:
                metadata = {}
            
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        INSERT INTO conversation_messages (id, conversation_id, role, content, metadata, timestamp)
                        VALUES (:id, :conversation_id, :role, :content, :metadata, NOW())
                    """)
                    await session.execute(sql, {
                        "id": message_id,
                        "conversation_id": conversation_id,
                        "role": role,
                        "content": content,
                        "metadata": json.dumps(metadata)
                    })
                    await session.commit()
            else:
                # 内存存储
                if conversation_id not in self._memory_store['conversation_messages']:
                    self._memory_store['conversation_messages'][conversation_id] = []
                
                self._memory_store['conversation_messages'][conversation_id].append({
                    'id': message_id,
                    'conversation_id': conversation_id,
                    'role': role,
                    'content': content,
                    'metadata': metadata,
                    'timestamp': datetime.now()
                })
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add message to conversation {conversation_id}: {e}")
            return False
    
    async def get_recent_messages(self, conversation_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的消息"""
        try:
            if self.is_connected:
                from backend.database.connection import get_async_db_session
                from sqlalchemy import text
                
                async with get_async_db_session() as session:
                    sql = text("""
                        SELECT * FROM conversation_messages 
                        WHERE conversation_id = :conversation_id 
                        ORDER BY timestamp DESC 
                        LIMIT :limit
                    """)
                    result = await session.execute(sql, {
                        "conversation_id": conversation_id,
                        "limit": limit
                    })
                    rows = result.fetchall()
                    messages = []
                    for row in reversed(rows):  # 反转以获得正确的时间顺序
                        data = dict(row._mapping)
                        if isinstance(data.get('metadata'), str):
                            data['metadata'] = json.loads(data['metadata'])
                        messages.append(data)
                    return messages
            else:
                # 内存存储
                messages = self._memory_store['conversation_messages'].get(conversation_id, [])
                return messages[-limit:] if messages else []
                
        except Exception as e:
            logger.error(f"Failed to get messages for conversation {conversation_id}: {e}")
            return []
