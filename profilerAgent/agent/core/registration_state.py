"""
RegistrationState - 注册状态管理
管理用户从手机验证到语音注册完成的整个流程
"""

import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class RegistrationStatus(Enum):
    """注册状态枚举"""
    PHONE_VERIFICATION_PENDING = "phone_verification_pending"
    PHONE_VERIFIED = "phone_verified"
    VOICE_REGISTRATION_INCOMPLETE = "voice_registration_incomplete"
    REGISTERED = "registered"
    FAILED = "failed"

@dataclass
class RequiredInfo:
    """必须收集的信息"""
    collected: bool = False
    value: Optional[str] = None
    attempts: int = 0
    last_question: Optional[str] = None
    last_attempt_time: Optional[datetime] = None

@dataclass
class OptionalInfo:
    """可选收集的信息"""
    collected: bool = False
    completion_rate: float = 0.0
    data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}

@dataclass
class RegistrationProgress:
    """注册进度跟踪"""
    user_id: str
    phone_number: str
    status: RegistrationStatus
    
    # 必须信息 (100%要求)
    required_info: Dict[str, RequiredInfo]
    
    # 可选信息 (75%要求)
    optional_info: Dict[str, OptionalInfo]
    
    # 对话上下文
    conversation_context: Dict[str, Any]
    
    # 时间戳
    created_at: datetime
    updated_at: datetime
    phone_verified_at: Optional[datetime] = None
    voice_started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    
    # 通话历史
    call_history: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.call_history is None:
            self.call_history = []
        
        # 初始化必须信息
        if not self.required_info:
            self.required_info = {
                "name": RequiredInfo(),
                "age": RequiredInfo(),
                "city": RequiredInfo(),
                "occupation": RequiredInfo()
            }
        
        # 初始化可选信息
        if not self.optional_info:
            self.optional_info = {
                "interests": OptionalInfo(),
                "personality": OptionalInfo(),
                "relationship_goals": OptionalInfo(),
                "lifestyle": OptionalInfo(),
                "values": OptionalInfo()
            }
        
        # 初始化对话上下文
        if not self.conversation_context:
            self.conversation_context = {
                "current_topic": None,
                "resistant_topics": [],
                "successful_topics": [],
                "last_question": None,
                "conversation_summary": ""
            }

    def calculate_completion_rates(self) -> tuple[bool, float]:
        """计算完成率"""
        # 必须信息完成率
        required_complete = all(info.collected for info in self.required_info.values())
        
        # 可选信息完成率
        optional_collected = sum(1 for info in self.optional_info.values() if info.collected)
        optional_rate = optional_collected / len(self.optional_info)
        
        return required_complete, optional_rate
    
    def is_registration_complete(self) -> bool:
        """检查注册是否完成"""
        required_complete, optional_rate = self.calculate_completion_rates()
        return required_complete and optional_rate >= 0.75
    
    def get_next_required_info(self) -> Optional[str]:
        """获取下一个需要收集的必须信息"""
        for field_name, info in self.required_info.items():
            if not info.collected:
                return field_name
        return None
    
    def get_missing_optional_info(self) -> List[str]:
        """获取缺失的可选信息"""
        return [field_name for field_name, info in self.optional_info.items() 
                if not info.collected]
    
    def update_required_info(self, field_name: str, value: str, question: str = None):
        """更新必须信息"""
        if field_name in self.required_info:
            self.required_info[field_name].collected = True
            self.required_info[field_name].value = value
            self.required_info[field_name].last_question = question
            self.required_info[field_name].last_attempt_time = datetime.now()
            self.updated_at = datetime.now()
            logger.info(f"Updated required info {field_name} for user {self.user_id}")
    
    def increment_attempts(self, field_name: str, question: str):
        """增加尝试次数"""
        if field_name in self.required_info:
            self.required_info[field_name].attempts += 1
            self.required_info[field_name].last_question = question
            self.required_info[field_name].last_attempt_time = datetime.now()
            self.updated_at = datetime.now()
    
    def update_optional_info(self, field_name: str, data: Dict[str, Any], completion_rate: float = 1.0):
        """更新可选信息"""
        if field_name in self.optional_info:
            self.optional_info[field_name].collected = True
            self.optional_info[field_name].data.update(data)
            self.optional_info[field_name].completion_rate = completion_rate
            self.updated_at = datetime.now()
            logger.info(f"Updated optional info {field_name} for user {self.user_id}")
    
    def add_call_record(self, duration: int, reason: str, progress_description: str):
        """添加通话记录"""
        call_record = {
            "timestamp": datetime.now().isoformat(),
            "duration": duration,
            "reason": reason,
            "progress": progress_description,
            "required_completion": self.calculate_completion_rates()[0],
            "optional_completion": self.calculate_completion_rates()[1]
        }
        self.call_history.append(call_record)
        self.updated_at = datetime.now()
    
    def update_conversation_context(self, **kwargs):
        """更新对话上下文"""
        self.conversation_context.update(kwargs)
        self.updated_at = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        data = asdict(self)
        # 处理枚举类型
        data['status'] = self.status.value
        # 处理datetime类型
        for field in ['created_at', 'updated_at', 'phone_verified_at', 'voice_started_at', 'completed_at']:
            if data[field]:
                data[field] = data[field].isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RegistrationProgress':
        """从字典创建实例"""
        # 处理枚举类型
        data['status'] = RegistrationStatus(data['status'])
        
        # 处理datetime类型
        for field in ['created_at', 'updated_at', 'phone_verified_at', 'voice_started_at', 'completed_at']:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        # 处理嵌套对象
        if 'required_info' in data:
            required_info = {}
            for field_name, info_data in data['required_info'].items():
                if info_data.get('last_attempt_time') and isinstance(info_data['last_attempt_time'], str):
                    info_data['last_attempt_time'] = datetime.fromisoformat(info_data['last_attempt_time'])
                required_info[field_name] = RequiredInfo(**info_data)
            data['required_info'] = required_info
        
        if 'optional_info' in data:
            optional_info = {}
            for field_name, info_data in data['optional_info'].items():
                optional_info[field_name] = OptionalInfo(**info_data)
            data['optional_info'] = optional_info
        
        return cls(**data)

class RegistrationStateManager:
    """注册状态管理器"""
    
    def __init__(self, redis_client=None, db_manager=None):
        self.redis = redis_client
        self.db = db_manager
        
        # 配置
        self.state_cache_ttl = 3600  # 1小时
        self.incomplete_cleanup_days = 7  # 7天后清理未完成注册
        
        logger.info("RegistrationStateManager initialized")
    
    async def create_registration(self, user_id: str, phone_number: str) -> RegistrationProgress:
        """创建新的注册进度"""
        progress = RegistrationProgress(
            user_id=user_id,
            phone_number=phone_number,
            status=RegistrationStatus.PHONE_VERIFICATION_PENDING,
            required_info={},
            optional_info={},
            conversation_context={},
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        await self._save_state(progress)
        logger.info(f"Created registration for user {user_id}")
        return progress
    
    async def get_registration_state(self, user_id: str) -> Optional[RegistrationProgress]:
        """获取注册状态"""
        # 先从Redis缓存获取
        if self.redis:
            try:
                cached_data = await self.redis.get(f"registration_state:{user_id}")
                if cached_data:
                    data = json.loads(cached_data)
                    return RegistrationProgress.from_dict(data)
            except Exception as e:
                logger.warning(f"Failed to get registration state from Redis: {e}")
        
        # 从数据库获取
        if self.db:
            try:
                # TODO: 实现数据库查询
                pass
            except Exception as e:
                logger.warning(f"Failed to get registration state from DB: {e}")
        
        return None
    
    async def _save_state(self, progress: RegistrationProgress):
        """保存状态到Redis和数据库"""
        data = progress.to_dict()
        
        # 保存到Redis缓存
        if self.redis:
            try:
                await self.redis.setex(
                    f"registration_state:{progress.user_id}",
                    self.state_cache_ttl,
                    json.dumps(data, default=str)
                )
            except Exception as e:
                logger.error(f"Failed to save registration state to Redis: {e}")
        
        # 保存到数据库
        if self.db:
            try:
                # TODO: 实现数据库保存
                pass
            except Exception as e:
                logger.error(f"Failed to save registration state to DB: {e}")
    
    async def update_status(self, user_id: str, status: RegistrationStatus) -> bool:
        """更新注册状态"""
        progress = await self.get_registration_state(user_id)
        if not progress:
            return False
        
        progress.status = status
        progress.updated_at = datetime.now()
        
        # 设置特定状态的时间戳
        if status == RegistrationStatus.PHONE_VERIFIED:
            progress.phone_verified_at = datetime.now()
        elif status == RegistrationStatus.VOICE_REGISTRATION_INCOMPLETE:
            progress.voice_started_at = datetime.now()
        elif status == RegistrationStatus.REGISTERED:
            progress.completed_at = datetime.now()
        
        await self._save_state(progress)
        logger.info(f"Updated registration status to {status.value} for user {user_id}")
        return True
    
    async def is_registration_complete(self, user_id: str) -> bool:
        """检查注册是否完成"""
        progress = await self.get_registration_state(user_id)
        if not progress:
            return False
        
        return progress.status == RegistrationStatus.REGISTERED
    
    async def cleanup_old_registrations(self) -> int:
        """清理过期的未完成注册"""
        # TODO: 实现清理逻辑
        logger.info("Cleaned up old incomplete registrations")
        return 0
