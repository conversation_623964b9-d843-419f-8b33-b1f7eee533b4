"""
UnifiedAgent - 统一AI Agent入口
根据用户状态路由到不同的专用Agent
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from .registration_agent import RegistrationAgent, RegistrationResponse
from .regular_conversation_agent import RegularConversationAgent, ConversationResponse
from .ai_service import AIService
from ..core.registration_state import RegistrationStateManager, RegistrationStatus
from ..core.profile_manager import ProfileManager
from ..core.memory_manager import MemoryManager
from ..core.database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class UnifiedAgentResponse:
    """统一Agent响应"""
    
    def __init__(self, content: str, agent_type: str, should_continue: bool = True,
                 registration_complete: bool = False, conversation_id: str = None,
                 progress: float = None, updated_info: Dict[str, Any] = None):
        self.content = content
        self.agent_type = agent_type  # "registration" or "conversation"
        self.should_continue = should_continue
        self.registration_complete = registration_complete
        self.conversation_id = conversation_id
        self.progress = progress
        self.updated_info = updated_info or {}
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            "content": self.content,
            "agent_type": self.agent_type,
            "should_continue": self.should_continue,
            "updated_info": self.updated_info
        }
        
        if self.registration_complete is not None:
            result["registration_complete"] = self.registration_complete
        if self.conversation_id:
            result["conversation_id"] = self.conversation_id
        if self.progress is not None:
            result["progress"] = self.progress
            
        return result

class UnifiedAgent:
    """统一AI Agent - 智能路由到专用Agent"""
    
    def __init__(self, registration_manager: RegistrationStateManager,
                 profile_manager: ProfileManager, memory_manager: MemoryManager,
                 db_manager: DatabaseManager, ai_service: AIService = None):
        """初始化统一Agent"""
        self.registration_manager = registration_manager
        self.profile_manager = profile_manager
        self.memory_manager = memory_manager
        self.db_manager = db_manager
        self.ai_service = ai_service or AIService()
        
        # 初始化专用Agents
        self.registration_agent = RegistrationAgent(
            registration_manager=registration_manager,
            profile_manager=profile_manager,
            memory_manager=memory_manager,
            db_manager=db_manager,
            ai_service=ai_service
        )
        
        self.conversation_agent = RegularConversationAgent(
            profile_manager=profile_manager,
            memory_manager=memory_manager,
            db_manager=db_manager,
            ai_service=ai_service
        )
        
        logger.info("UnifiedAgent initialized")
    
    async def process_input(self, user_id: str, user_input: str, 
                          conversation_type: str = "chat") -> UnifiedAgentResponse:
        """处理用户输入 - 智能路由"""
        try:
            # 检查用户注册状态
            user_status = await self._get_user_status(user_id)
            
            if user_status == "not_registered":
                # 新用户，开始注册流程
                return await self._handle_new_user(user_id, user_input)
            
            elif user_status == "registration_incomplete":
                # 注册进行中，继续注册流程
                return await self._handle_registration_in_progress(user_id, user_input)
            
            elif user_status == "registered":
                # 已注册用户，正常对话
                return await self._handle_registered_user(user_id, user_input, conversation_type)
            
            else:
                # 未知状态，默认处理
                logger.warning(f"Unknown user status for {user_id}: {user_status}")
                return await self._handle_registered_user(user_id, user_input, conversation_type)
                
        except Exception as e:
            logger.error(f"Failed to process input for user {user_id}: {e}")
            return UnifiedAgentResponse(
                content="Sorry, there's a system issue. Please try again later.",
                agent_type="error",
                should_continue=False
            )
    
    async def start_conversation(self, user_id: str, phone_number: str = None,
                               conversation_type: str = "chat") -> UnifiedAgentResponse:
        """开始对话 - 自动判断用户状态"""
        try:
            user_status = await self._get_user_status(user_id)
            
            if user_status == "not_registered":
                # 新用户，开始注册
                if not phone_number:
                    return UnifiedAgentResponse(
                        content="Welcome! Please provide your phone number to start registration.",
                        agent_type="registration",
                        should_continue=True
                    )
                
                response = await self.registration_agent.start_registration(user_id, phone_number)
                return self._convert_registration_response(response)
            
            elif user_status == "registration_incomplete":
                # 继续未完成的注册
                return UnifiedAgentResponse(
                    content="Welcome back! Let's continue completing your registration.",
                    agent_type="registration",
                    should_continue=True
                )
            
            else:
                # 已注册用户，开始正常对话
                response = await self.conversation_agent.start_conversation(user_id, conversation_type)
                return self._convert_conversation_response(response)
                
        except Exception as e:
            logger.error(f"Failed to start conversation for user {user_id}: {e}")
            return UnifiedAgentResponse(
                content="Hello! Nice to meet you.",
                agent_type="conversation",
                should_continue=True
            )
    
    async def _get_user_status(self, user_id: str) -> str:
        """获取用户状态"""
        try:
            # 检查注册状态
            registration_complete = await self.registration_manager.is_registration_complete(user_id)
            if registration_complete:
                return "registered"
            
            # 检查是否有注册进度
            progress = await self.registration_manager.get_registration_state(user_id)
            if progress:
                return "registration_incomplete"
            
            # 检查数据库中是否有用户记录
            user = await self.db_manager.get_user(user_id)
            if user:
                if user.get("status") == "active":
                    return "registered"
                else:
                    return "registration_incomplete"
            
            return "not_registered"
            
        except Exception as e:
            logger.error(f"Failed to get user status for {user_id}: {e}")
            return "not_registered"
    
    async def _handle_new_user(self, user_id: str, user_input: str) -> UnifiedAgentResponse:
        """处理新用户"""
        try:
            # 尝试从输入中提取手机号
            phone_number = self._extract_phone_number(user_input)
            
            if phone_number:
                # 开始注册流程
                response = await self.registration_agent.start_registration(user_id, phone_number)
                return self._convert_registration_response(response)
            else:
                # 请求手机号
                return UnifiedAgentResponse(
                    content="Welcome! To start registration, please tell me your phone number.",
                    agent_type="registration",
                    should_continue=True
                )
                
        except Exception as e:
            logger.error(f"Failed to handle new user {user_id}: {e}")
            return UnifiedAgentResponse(
                content="Welcome! Please tell me your phone number to start registration.",
                agent_type="registration",
                should_continue=True
            )
    
    async def _handle_registration_in_progress(self, user_id: str, user_input: str) -> UnifiedAgentResponse:
        """处理注册进行中的用户"""
        try:
            response = await self.registration_agent.process_registration_input(user_id, user_input)
            return self._convert_registration_response(response)
            
        except Exception as e:
            logger.error(f"Failed to handle registration for user {user_id}: {e}")
            return UnifiedAgentResponse(
                content="There was an issue during registration. Please try again.",
                agent_type="registration",
                should_continue=True
            )
    
    async def _handle_registered_user(self, user_id: str, user_input: str, 
                                    conversation_type: str) -> UnifiedAgentResponse:
        """处理已注册用户"""
        try:
            response = await self.conversation_agent.process_conversation(
                user_id, user_input, conversation_type
            )
            return self._convert_conversation_response(response)
            
        except Exception as e:
            logger.error(f"Failed to handle conversation for user {user_id}: {e}")
            return UnifiedAgentResponse(
                content="Sorry, I didn't catch that. Could you say that again?",
                agent_type="conversation",
                should_continue=True
            )
    
    def _extract_phone_number(self, text: str) -> Optional[str]:
        """从文本中提取手机号"""
        import re
        
        # Match various phone number formats
        patterns = [
            r'1[3-9]\d{9}',  # Chinese mobile number
            r'\+86\s*1[3-9]\d{9}',  # With country code
            r'\+1\s*\d{10}',  # US phone number
            r'\+\d{1,3}\s*\d{10,11}'  # Other international numbers
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.replace(' ', '').replace('-', ''))
            if match:
                return match.group(0)
        
        return None
    
    def _convert_registration_response(self, response: RegistrationResponse) -> UnifiedAgentResponse:
        """转换注册响应"""
        return UnifiedAgentResponse(
            content=response.content,
            agent_type="registration",
            should_continue=response.should_continue,
            registration_complete=response.registration_complete,
            progress=response.progress,
            updated_info=response.collected_info
        )
    
    def _convert_conversation_response(self, response: ConversationResponse) -> UnifiedAgentResponse:
        """转换对话响应"""
        return UnifiedAgentResponse(
            content=response.content,
            agent_type="conversation",
            should_continue=not response.should_end,
            conversation_id=response.conversation_id,
            updated_info=response.updated_info
        )
    
    async def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取用户信息摘要"""
        try:
            user_status = await self._get_user_status(user_id)
            result = {"user_id": user_id, "status": user_status}
            
            if user_status == "registered":
                # 获取用户档案
                profile = await self.profile_manager.get_profile(user_id)
                if profile:
                    result["profile"] = profile
                
                # 获取对话上下文
                context = await self.memory_manager.get_conversation_context(user_id)
                result["recent_topics"] = context.get("important_topics", [])
                
            elif user_status == "registration_incomplete":
                # 获取注册进度
                progress = await self.registration_manager.get_registration_state(user_id)
                if progress:
                    required_complete, optional_rate = progress.calculate_completion_rates()
                    result["registration_progress"] = {
                        "required_complete": required_complete,
                        "optional_completion_rate": optional_rate,
                        "next_required_field": progress.get_next_required_info()
                    }
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get user info for {user_id}: {e}")
            return {"user_id": user_id, "status": "unknown", "error": str(e)}
    
    async def reset_user(self, user_id: str) -> bool:
        """重置用户状态（用于测试）"""
        try:
            # 清除注册状态
            await self.registration_manager.cleanup_old_registrations()
            
            # 清除记忆
            await self.memory_manager.clear_memory(user_id)
            
            logger.info(f"Reset user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to reset user {user_id}: {e}")
            return False
